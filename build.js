const fs = require('fs');
const path = require('path');

// Configuration
const config = {
    srcDir: './src',
    distDir: './dist',
    htmlFile: 'index.html',
    cssFile: 'css/styles.css',
    jsFile: 'js/script.js',
    outputFile: 'index.html'
};

// Utility function to ensure directory exists
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
}

// Function to read file content
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
        process.exit(1);
    }
}

// Function to write file content
function writeFile(filePath, content) {
    try {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Successfully wrote: ${filePath}`);
    } catch (error) {
        console.error(`Error writing file ${filePath}:`, error.message);
        process.exit(1);
    }
}

// Function to minify CSS (basic minification)
function minifyCSS(css) {
    return css
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
        .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
        .replace(/;\s*}/g, '}') // Remove semicolon before closing brace
        .replace(/\s*{\s*/g, '{') // Remove spaces around opening brace
        .replace(/;\s*/g, ';') // Remove spaces after semicolon
        .replace(/,\s*/g, ',') // Remove spaces after comma
        .replace(/:\s*/g, ':') // Remove spaces after colon
        .trim();
}

// Function to minify JavaScript (basic minification)
function minifyJS(js) {
    return js
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
        .replace(/\/\/.*$/gm, '') // Remove line comments
        .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
        .replace(/;\s*}/g, ';}') // Ensure semicolon before closing brace
        .replace(/\s*{\s*/g, '{') // Remove spaces around opening brace
        .replace(/\s*}\s*/g, '}') // Remove spaces around closing brace
        .replace(/;\s*/g, ';') // Remove spaces after semicolon
        .replace(/,\s*/g, ',') // Remove spaces after comma
        .trim();
}

// Function to copy images to dist directory
function copyImages() {
    const srcImagesDir = path.join(config.srcDir, 'images');
    const distImagesDir = path.join(config.distDir, 'images');
    
    if (fs.existsSync(srcImagesDir)) {
        ensureDirectoryExists(distImagesDir);
        
        const imageFiles = fs.readdirSync(srcImagesDir);
        imageFiles.forEach(file => {
            const srcPath = path.join(srcImagesDir, file);
            const distPath = path.join(distImagesDir, file);
            
            if (fs.statSync(srcPath).isFile()) {
                fs.copyFileSync(srcPath, distPath);
                console.log(`Copied image: ${file}`);
            }
        });
    }
}

// Main build function
function build() {
    console.log('🚀 Starting CloudNextAI production build...\n');
    
    // Ensure dist directory exists
    ensureDirectoryExists(config.distDir);
    
    // Read source files
    console.log('📖 Reading source files...');
    const htmlContent = readFile(path.join(config.srcDir, config.htmlFile));
    const cssContent = readFile(path.join(config.srcDir, config.cssFile));
    const jsContent = readFile(path.join(config.srcDir, config.jsFile));
    
    // Minify CSS and JS
    console.log('🗜️  Minifying assets...');
    const minifiedCSS = minifyCSS(cssContent);
    const minifiedJS = minifyJS(jsContent);
    
    // Replace external references with inline content
    console.log('🔗 Bundling assets into HTML...');
    let bundledHTML = htmlContent;
    
    // Replace CSS link with inline styles
    const cssLinkRegex = /<link\s+rel="stylesheet"\s+href="css\/styles\.css">/;
    const inlineCSS = `<style>\n${minifiedCSS}\n    </style>`;
    bundledHTML = bundledHTML.replace(cssLinkRegex, inlineCSS);
    
    // Replace JS script tag with inline script
    const jsScriptRegex = /<script\s+src="js\/script\.js"><\/script>/;
    const inlineJS = `<script>\n${minifiedJS}\n    </script>`;
    bundledHTML = bundledHTML.replace(jsScriptRegex, inlineJS);
    
    // Write the bundled HTML file
    const outputPath = path.join(config.distDir, config.outputFile);
    writeFile(outputPath, bundledHTML);
    
    // Copy images
    console.log('🖼️  Copying images...');
    copyImages();
    
    // Calculate file sizes
    const originalHTMLSize = fs.statSync(path.join(config.srcDir, config.htmlFile)).size;
    const originalCSSSize = fs.statSync(path.join(config.srcDir, config.cssFile)).size;
    const originalJSSize = fs.statSync(path.join(config.srcDir, config.jsFile)).size;
    const bundledSize = fs.statSync(outputPath).size;
    
    const totalOriginalSize = originalHTMLSize + originalCSSSize + originalJSSize;
    
    console.log('\n📊 Build Statistics:');
    console.log(`Original HTML: ${(originalHTMLSize / 1024).toFixed(2)} KB`);
    console.log(`Original CSS: ${(originalCSSSize / 1024).toFixed(2)} KB`);
    console.log(`Original JS: ${(originalJSSize / 1024).toFixed(2)} KB`);
    console.log(`Total Original: ${(totalOriginalSize / 1024).toFixed(2)} KB`);
    console.log(`Bundled HTML: ${(bundledSize / 1024).toFixed(2)} KB`);
    
    const compressionRatio = ((totalOriginalSize - bundledSize) / totalOriginalSize * 100);
    if (compressionRatio > 0) {
        console.log(`Compression: ${compressionRatio.toFixed(1)}% smaller`);
    } else {
        console.log(`Size increase: ${Math.abs(compressionRatio).toFixed(1)}% larger (due to bundling overhead)`);
    }
    
    console.log('\n✅ Build completed successfully!');
    console.log(`📁 Production files are in: ${config.distDir}/`);
    console.log(`🌐 Open ${outputPath} in your browser to test the production build.`);
}

// Run the build
if (require.main === module) {
    build();
}

module.exports = { build };
