# Email Setup Guide for CloudNextAI Contact Form

## Option 1: <PERSON>ailJS (Recommended - No Backend Required)

EmailJS allows you to send emails directly from your static website without needing a server.

### Step 1: Create EmailJS Account

1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Sign up for a free account (allows 200 emails/month)
3. Verify your email address

### Step 2: Set Up Email Service

1. In your EmailJS dashboard, go to **Email Services**
2. Click **Add New Service**
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions to connect your email account
5. Note down your **Service ID** (e.g., `service_abc123`)

### Step 3: Create Email Template

1. Go to **Email Templates** in your dashboard
2. Click **Create New Template**
3. Use this template content:

```
Subject: New Contact Form Submission - CloudNextAI

From: {{from_name}} <{{from_email}}>
Phone: {{phone}}
Company: {{company}}
Service Interest: {{service}}

Message:
{{message}}

---
This message was sent from the CloudNextAI contact form.
Reply directly to this email to respond to the inquiry.
```

4. Save the template and note down your **Template ID** (e.g., `template_xyz789`)

### Step 4: Get Your Public Key

1. Go to **Account** → **General**
2. Find your **Public Key** (e.g., `user_abcdef123456`)

### Step 5: Update Your Code

Replace the placeholders in `src/js/script.js`:

```javascript
// Replace these values with your actual EmailJS credentials
emailjs.init("YOUR_PUBLIC_KEY");           // Your public key
// In the emailjs.send() call:
emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', templateParams)
```

And update your email address:
```javascript
to_email: '<EMAIL>' // Replace with your actual email
```

### Step 6: Test the Form

1. Run `npm run build` to rebuild your site
2. Open `dist/index.html` in your browser
3. Fill out and submit the contact form
4. Check your email inbox for the message

---

## Option 2: Formspree (Alternative - No Backend Required)

If you prefer Formspree, here's how to set it up:

### Step 1: Create Formspree Account

1. Go to [https://formspree.io/](https://formspree.io/)
2. Sign up for a free account (allows 50 submissions/month)

### Step 2: Create a Form

1. In your dashboard, click **New Form**
2. Enter a form name (e.g., "CloudNextAI Contact")
3. Note down your form endpoint URL

### Step 3: Update HTML Form

Replace the form tag in `src/index.html`:

```html
<form id="contact-form" action="https://formspree.io/f/YOUR_FORM_ID" method="POST">
```

### Step 4: Update JavaScript

Replace the contact form handling in `src/js/script.js`:

```javascript
document.getElementById('contact-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Simple validation
    const data = Object.fromEntries(formData);
    if (!data.firstName || !data.lastName || !data.email || !data.message) {
        alert('Please fill in all required fields.');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('.submit-btn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span>Sending...</span>';
    submitBtn.disabled = true;
    
    // Submit to Formspree
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (response.ok) {
            // Show success message
            const successMessage = document.getElementById('success-message');
            successMessage.classList.add('active');
            this.reset();
            setTimeout(() => {
                successMessage.classList.remove('active');
            }, 5000);
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Sorry, there was an error sending your message. Please try again.');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
```

---

## Option 3: Netlify Forms (If hosting on Netlify)

If you're hosting on Netlify, you can use their built-in form handling:

### Step 1: Add Netlify Form Attributes

Update the form tag in `src/index.html`:

```html
<form id="contact-form" name="contact" method="POST" data-netlify="true" netlify-honeypot="bot-field">
    <input type="hidden" name="form-name" value="contact" />
    <!-- Add honeypot field for spam protection -->
    <div style="display: none;">
        <input name="bot-field" />
    </div>
    <!-- Rest of your form fields -->
```

### Step 2: Update JavaScript

Use a simpler JavaScript approach since Netlify handles the backend:

```javascript
document.getElementById('contact-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Simple validation
    const data = Object.fromEntries(formData);
    if (!data.firstName || !data.lastName || !data.email || !data.message) {
        alert('Please fill in all required fields.');
        return;
    }
    
    // Submit to Netlify
    fetch('/', {
        method: 'POST',
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams(formData).toString()
    })
    .then(() => {
        // Show success message
        const successMessage = document.getElementById('success-message');
        successMessage.classList.add('active');
        this.reset();
        setTimeout(() => {
            successMessage.classList.remove('active');
        }, 5000);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Sorry, there was an error sending your message. Please try again.');
    });
});
```

---

## Recommendation

**I recommend Option 1 (EmailJS)** because:
- ✅ Works with any hosting provider
- ✅ Free tier includes 200 emails/month
- ✅ Easy to set up and customize
- ✅ Emails come directly to your inbox
- ✅ No server required
- ✅ Good spam protection

Choose the option that best fits your hosting setup and preferences!
