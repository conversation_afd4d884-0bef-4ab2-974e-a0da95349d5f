document.addEventListener('DOMContentLoaded', function () {
    const header = document.getElementById('header');
    const menuToggle = document.getElementById('menu-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // --- Header Scroll Effect ---
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // --- Mobile Menu Toggle ---
    menuToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        menuToggle.classList.toggle('active');
    });
    
    // --- Close mobile menu when a link is clicked ---
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });

    

    // Initialize EmailJS
    emailjs.init("kMZFQWGcSXZY5bt9t"); // Replace with your EmailJS public key

    const contactForm = document.getElementById('contact-form');

    // Contact Form Handling
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Simple validation
            if (!data.firstName || !data.lastName || !data.email || !data.message) {
                alert('Please fill in all required fields.');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span>Sending...</span>';
            submitBtn.disabled = true;

            // Prepare email parameters
            const templateParams = {
                from_name: `${data.firstName} ${data.lastName}`,
                from_email: data.email,
                phone: data.phone || 'Not provided',
                company: data.company || 'Not provided',
                service: data.service || 'Not specified',
                message: data.message,
                to_email: '<EMAIL>' // Replace with your email
            };

            // Send email using EmailJS
            emailjs.send('service_pefwgmy', 'template_nukqeqb', templateParams)
                .then(function(response) {
                    console.log('Email sent successfully:', response);

                    // Show success message
                    const successMessage = document.getElementById('success-message');
                    successMessage.classList.add('active');

                    // Reset form
                    document.getElementById('contact-form').reset();

                    // Hide success message after 5 seconds
                    setTimeout(() => {
                        successMessage.classList.remove('active');
                    }, 5000);

                }, function(error) {
                    console.error('Email sending failed:', error);
                    alert('Sorry, there was an error sending your message. Please try again or contact us directly.');
                })
                .finally(function() {
                    // Reset button state
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
    });
    }

    // --- Animate on Scroll ---
    const scrollElements = document.querySelectorAll('.animate-on-scroll');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });

    scrollElements.forEach(el => {
        observer.observe(el);
    });
    
    // --- Services Carousel ---
    const carousel = document.getElementById('services-carousel');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const indicators = document.querySelectorAll('.indicator');
    let currentSlide = 1; // Start at slide 1 (first real slide, after clone)
    const totalSlides = 3;
    let isTransitioning = false;

    function updateCarousel(smooth = true) {
        if (smooth) {
            carousel.style.transition = 'transform 0.5s ease-in-out';
        } else {
            carousel.style.transition = 'none';
        }

        const translateX = -currentSlide * 100;
        carousel.style.transform = `translateX(${translateX}%)`;

        // Update indicators based on real slide position
        let indicatorIndex;
        if (currentSlide === 0) indicatorIndex = 2; // Clone of last slide
        else if (currentSlide === 4) indicatorIndex = 0; // Clone of first slide
        else indicatorIndex = currentSlide - 1; // Real slides (1,2,3 -> 0,1,2)

        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === indicatorIndex);
        });
    }

    function nextSlide() {
        if (isTransitioning) return;
        isTransitioning = true;

        currentSlide++;
        updateCarousel();

        // If we're at the clone of the first slide (position 4), jump to real first slide (position 1)
        if (currentSlide === 4) {
            setTimeout(() => {
                currentSlide = 1;
                updateCarousel(false); // Jump without animation
                setTimeout(() => {
                    isTransitioning = false;
                }, 50);
            }, 500);
        } else {
            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        }
    }

    function prevSlide() {
        if (isTransitioning) return;
        isTransitioning = true;

        currentSlide--;
        updateCarousel();

        // If we're at the clone of the last slide (position 0), jump to real last slide (position 3)
        if (currentSlide === 0) {
            setTimeout(() => {
                currentSlide = 3;
                updateCarousel(false); // Jump without animation
                setTimeout(() => {
                    isTransitioning = false;
                }, 50);
            }, 500);
        } else {
            setTimeout(() => {
                isTransitioning = false;
            }, 500);
        }
    }

    function goToSlide(slideIndex) {
        if (isTransitioning) return;
        isTransitioning = true;

        currentSlide = slideIndex + 1; // Convert indicator index to real slide position
        updateCarousel();
        setTimeout(() => {
            isTransitioning = false;
        }, 500);
    }

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => goToSlide(index));
    });

    // Auto-play carousel with smooth infinite looping
    let autoPlayInterval;

    function startAutoPlay() {
        autoPlayInterval = setInterval(() => {
            nextSlide();
        }, 4000); // Change slide every 4 seconds
    }

    function stopAutoPlay() {
        clearInterval(autoPlayInterval);
    }

    // Start auto-play when page loads
    startAutoPlay();

    // Pause auto-play on hover
    const carouselContainer = document.querySelector('.services-carousel-container');
    carouselContainer.addEventListener('mouseenter', stopAutoPlay);
    carouselContainer.addEventListener('mouseleave', startAutoPlay);

    // Initialize carousel
    updateCarousel();

    // --- Process Carousel ---
    const processCarousel = document.getElementById('process-carousel');
    const processPrevBtn = document.getElementById('process-prev-btn');
    const processNextBtn = document.getElementById('process-next-btn');
    const processIndicators = document.querySelectorAll('.process-indicator');
    let currentProcessSlide = 1; // Start at slide 1 (first real slide, after clone)
    const totalProcessSlides = 3;
    let isProcessTransitioning = false;

    function updateProcessCarousel(smooth = true) {
        if (smooth) {
            processCarousel.style.transition = 'transform 0.5s ease-in-out';
        } else {
            processCarousel.style.transition = 'none';
        }

        const translateX = -currentProcessSlide * 100;
        processCarousel.style.transform = `translateX(${translateX}%)`;

        // Update indicators based on real slide position
        let indicatorIndex;
        if (currentProcessSlide === 0) indicatorIndex = 2; // Clone of last slide
        else if (currentProcessSlide === 4) indicatorIndex = 0; // Clone of first slide
        else indicatorIndex = currentProcessSlide - 1; // Real slides (1,2,3 -> 0,1,2)

        processIndicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === indicatorIndex);
        });
    }

    function nextProcessSlide() {
        if (isProcessTransitioning) return;
        isProcessTransitioning = true;

        currentProcessSlide++;
        updateProcessCarousel();

        // If we're at the clone of the first slide (position 4), jump to real first slide (position 1)
        if (currentProcessSlide === 4) {
            setTimeout(() => {
                currentProcessSlide = 1;
                updateProcessCarousel(false); // Jump without animation
                setTimeout(() => {
                    isProcessTransitioning = false;
                }, 50);
            }, 500);
        } else {
            setTimeout(() => {
                isProcessTransitioning = false;
            }, 500);
        }
    }

    function prevProcessSlide() {
        if (isProcessTransitioning) return;
        isProcessTransitioning = true;

        currentProcessSlide--;
        updateProcessCarousel();

        // If we're at the clone of the last slide (position 0), jump to real last slide (position 3)
        if (currentProcessSlide === 0) {
            setTimeout(() => {
                currentProcessSlide = 3;
                updateProcessCarousel(false); // Jump without animation
                setTimeout(() => {
                    isProcessTransitioning = false;
                }, 50);
            }, 500);
        } else {
            setTimeout(() => {
                isProcessTransitioning = false;
            }, 500);
        }
    }

    function goToProcessSlide(slideIndex) {
        if (isProcessTransitioning) return;
        isProcessTransitioning = true;

        currentProcessSlide = slideIndex + 1; // Convert indicator index to real slide position
        updateProcessCarousel();
        setTimeout(() => {
            isProcessTransitioning = false;
        }, 500);
    }

    // Event listeners for process carousel
    processNextBtn.addEventListener('click', nextProcessSlide);
    processPrevBtn.addEventListener('click', prevProcessSlide);

    processIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => goToProcessSlide(index));
    });

    // Auto-play process carousel with smooth infinite looping
    let processAutoPlayInterval;

    function startProcessAutoPlay() {
        processAutoPlayInterval = setInterval(() => {
            nextProcessSlide();
        }, 4500); // Change slide every 4.5 seconds (slightly different from services)
    }

    function stopProcessAutoPlay() {
        clearInterval(processAutoPlayInterval);
    }

    // Start auto-play when page loads
    startProcessAutoPlay();

    // Pause auto-play on hover
    const processCarouselContainer = document.querySelector('.process-carousel-container');
    processCarouselContainer.addEventListener('mouseenter', stopProcessAutoPlay);
    processCarouselContainer.addEventListener('mouseleave', startProcessAutoPlay);

    // Initialize process carousel
    updateProcessCarousel();

    // --- Active Nav Link on Scroll ---
    const sections = document.querySelectorAll('section[id]');
    window.addEventListener('scroll', () => {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (pageYOffset >= sectionTop - 85) { // 85px offset for header
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href').includes(current)) {
                link.classList.add('active');
            }
        });
    });

    // Hero Carousel functionality (namespaced to avoid collisions)
    const heroCarousel = document.querySelector('.hero-carousel');
    if (heroCarousel) {
        const heroSlides = heroCarousel.querySelectorAll('.carousel-slide');
        const heroDots = heroCarousel.querySelectorAll('.dot');
        const heroPrevBtn = heroCarousel.querySelector('.prev-btn');
        const heroNextBtn = heroCarousel.querySelector('.next-btn');
        let heroCurrentSlide = 0;
        let heroAutoSlideInterval;

        function heroShowSlide(index) {
            // Remove active class from all slides and dots
            heroSlides.forEach(slide => slide.classList.remove('active'));
            heroDots.forEach(dot => dot.classList.remove('active'));

            // Add active class to current slide and dot
            heroSlides[index].classList.add('active');
            heroDots[index].classList.add('active');
            heroCurrentSlide = index;
        }

        function heroNextSlide() {
            const next = (heroCurrentSlide + 1) % heroSlides.length;
            heroShowSlide(next);
        }

        function heroPrevSlide() {
            const prev = (heroCurrentSlide - 1 + heroSlides.length) % heroSlides.length;
            heroShowSlide(prev);
        }

        function startHeroAutoSlide() {
            heroAutoSlideInterval = setInterval(heroNextSlide, 4000); // Change slide every 4 seconds
        }

        function stopHeroAutoSlide() {
            clearInterval(heroAutoSlideInterval);
        }

        // Event listeners
        heroNextBtn.addEventListener('click', () => {
            heroNextSlide();
            stopHeroAutoSlide();
            startHeroAutoSlide(); // Restart auto-slide after manual interaction
        });

        heroPrevBtn.addEventListener('click', () => {
            heroPrevSlide();
            stopHeroAutoSlide();
            startHeroAutoSlide(); // Restart auto-slide after manual interaction
        });

        heroDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                heroShowSlide(index);
                stopHeroAutoSlide();
                startHeroAutoSlide(); // Restart auto-slide after manual interaction
            });
        });

        // Pause auto-slide on hover
        heroCarousel.addEventListener('mouseenter', stopHeroAutoSlide);
        heroCarousel.addEventListener('mouseleave', startHeroAutoSlide);

        // Start auto-slide
        startHeroAutoSlide();
    }
});
