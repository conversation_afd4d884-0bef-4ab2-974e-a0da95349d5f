/* --- Base & Typography --- */
:root {
    --theme-primary: #667eea;
    --theme-secondary: #764ba2;
    --text-light: #ffffff;
    --text-muted: #b0b0b0;
    --bg-dark: #0a0a0a;
    --bg-darker: #0f0f0f;
    --bg-panel: rgba(255, 255, 255, 0.05);
    --border-color: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: var(--bg-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* --- Header --- */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid transparent;
}

header.scrolled {
    border-bottom: 1px solid var(--border-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    transition: width 0.3s ease;
}

nav a:hover, nav a.active {
    color: var(--theme-primary);
}

nav a:hover::after, nav a.active::after {
    width: 100%;
}

.cta-header {
    padding: 10px 25px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.cta-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
    z-index: 1001;
}

.menu-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-light);
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* --- Hero Section --- */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px; 
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.2) 0%, transparent 40%);
    animation: pulse 5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: var(--text-muted);
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.cta-button {
    display: inline-block;
    padding: 15px 35px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: transparent;
    border-color: var(--theme-primary);
}

.btn-secondary:hover {
    background: var(--theme-primary);
    color: white;
}

.hero-visual {
    position: relative;
    height: 400px;
    background: var(--bg-panel);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 6rem;
}

/* --- Sections General --- */
section {
    padding: 100px 0;
}

/* --- Trust Section --- */
.trust-section {
    display:none;
    padding: 60px 0;
    background: #0c0c0c;
    text-align: center;
}

.trust-title {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 30px;
}

.trust-logos {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
    opacity: 0.6;
    filter: grayscale(100%);
}

.trust-logo {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 1.2rem;
}

/* --- Services Section --- */
.services {
    background: var(--bg-darker);
}

.services-carousel-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 20px;
    padding-bottom: 60px; /* Space for navigation controls */
}

.services-carousel {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.service-slide {
    min-width: 100%;
    display: flex;
    gap: 30px;
    padding: 15px 20px 5px 20px; /* Added top padding for hover effect */
}

.service-card {
    background: var(--bg-panel);
    padding: 40px;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    flex: 1;
    min-width: 280px;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    border-color: var(--theme-primary);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-light);
}

.service-card p {
    color: var(--text-muted);
    line-height: 1.6;
}

/* Services Carousel Navigation */
.services-carousel-container .carousel-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.services-carousel-container .carousel-btn {
    background: var(--bg-panel);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.services-carousel-container .carousel-btn:hover {
    background: var(--theme-primary);
    border-color: var(--theme-primary);
    transform: scale(1.1);
}

.services-carousel-container .carousel-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.services-carousel-container .carousel-btn:disabled:hover {
    background: var(--bg-panel);
    border-color: var(--border-color);
}

/* Carousel Indicators */
.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: var(--theme-primary);
    transform: scale(1.2);
}

/* Responsive Design for Services */
@media (max-width: 768px) {
    .service-slide {
        flex-direction: column;
        gap: 20px;
    }

    .service-card {
        min-width: auto;
    }

    .services-carousel-container .carousel-nav {
        gap: 15px;
        margin-top: 30px;
    }

    .services-carousel-container .carousel-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

/* --- Case Studies Section --- */
.case-studies {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.case-studies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.case-study {
    background: var(--bg-panel);
    padding: 30px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.case-study:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
}

.case-study-tag {
    display: inline-block;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-secondary));
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-bottom: 15px;
}

.case-study h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.case-study p {
    color: var(--text-muted);
    margin-bottom: 15px;
}

.case-study-results {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;
}

.result-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.result-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--theme-primary);
}

.result-label {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Testimonials Section */
.testimonials {
    display:none;
    padding: 100px 0;
    background: #0f0f0f;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.testimonial {
    background: rgba(255, 255, 255, 0.05);
    padding: 30px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: 10px;
    left: 20px;
    font-size: 3rem;
    color: #667eea;
    opacity: 0.3;
}

.testimonial-text {
    color: #b0b0b0;
    margin-bottom: 20px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
}

.author-info h4 {
    color: #ffffff;
    margin-bottom: 5px;
}

.author-info p {
    color: #b0b0b0;
    font-size: 0.9rem;
}

/* Process Section */
.process {
    padding: 100px 0;
    background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
}

.process-carousel-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 20px;
    padding-bottom: 60px; /* Space for navigation controls */
}

.process-carousel {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.process-slide {
    min-width: 100%;
    display: flex;
    gap: 30px;
    padding: 15px 20px 5px 20px; /* Added top padding for hover effect */
}

.process-step {
    text-align: center;
    position: relative;
    background: var(--bg-panel);
    padding: 40px 30px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    flex: 1;
    min-width: 280px;
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    border-color: var(--theme-primary);
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin: 0 auto 20px;
}

.process-step h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #ffffff;
}

.process-step p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Process Carousel Navigation */
.process-carousel-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 40px;
}

.process-carousel-btn {
    background: var(--bg-panel);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.process-carousel-btn:hover {
    background: var(--theme-primary);
    border-color: var(--theme-primary);
    transform: scale(1.1);
}

.process-carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.process-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.process-indicator.active {
    background: var(--theme-primary);
    transform: scale(1.2);
}

/* Responsive Design for Process */
@media (max-width: 768px) {
    .process-slide {
        flex-direction: column;
        gap: 20px;
    }

    .process-step {
        min-width: auto;
    }

    .process-carousel-nav {
        gap: 15px;
        margin-top: 30px;
    }

    .process-carousel-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    /* Case Studies Section Mobile */
    .case-studies {
        padding: 60px 0;
    }

    .case-studies-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .case-study {
        padding: 25px 20px;
    }

    .case-study h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .case-study p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    .case-study-tags {
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 15px;
    }

    .case-study-tag {
        font-size: 0.8rem;
        padding: 5px 10px;
    }
}

/* About Section */
.about {
    padding: 100px 0;
    background: #0f0f0f;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.about-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.about-hero-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.about-hero-image:hover {
    transform: translateY(-10px);
}

/* Hero Carousel Styles */
.hero-carousel {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 20px;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide.active {
    opacity: 1;
}

.hero-carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.hero-carousel-image:hover {
    transform: translateY(-10px);
}

/* Hero Carousel Navigation */
.hero-carousel .carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.hero-carousel .carousel-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 24px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-carousel .carousel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--theme-primary);
    transform: scale(1.2);
}

.dot:hover {
    background: rgba(255, 255, 255, 0.6);
}

.about-text h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-text p {
    font-size: 1.1rem;
    color: #b0b0b0;
    margin-bottom: 20px;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 30px;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
}

.about-feature-icon {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* New feature cards grid for right side */
.about-features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.about-feature-card {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.about-feature-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.about-feature-card h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.about-feature-card p {
    margin: 0;
    font-size: 14px;
    color: #b0b0b0;
    line-height: 1.5;
}

.stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.1);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.stat-label {
    color: #b0b0b0;
    font-size: 1rem;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a2e 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-info p {
    color: #b0b0b0;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.contact-item-icon {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Contact Form */
.contact-form {
    background: rgba(255, 255, 255, 0.05);
    padding: 40px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.success-message {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: none;
}

.success-message.active {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #ffffff;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.form-group textarea {
    height: 120px;
    resize: vertical;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.submit-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #764ba2, #667eea);
    transition: left 0.3s ease;
}

.submit-btn:hover::before {
    left: 0;
}

.submit-btn span {
    position: relative;
    z-index: 1;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* CTA Section */
.cta-section {
    display:none;
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: white;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-white {
    background: white;
    color: #667eea;
    border: none;
}

.btn-white:hover {
    background: #f0f0f0;
    transform: translateY(-2px);
}

/* Footer */
footer {
    background: #0a0a0a;
    padding: 80px 0 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    margin-bottom: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-section p, .footer-section a {
    color: #b0b0b0;
    text-decoration: none;
    line-height: 1.8;
    margin-bottom: 10px;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-bottom {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #666;
}

.footer-bottom p {
    margin-bottom: 10px;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    .header-content {
        position: relative;
    }

    .menu-toggle {
        display: flex;
    }

    .cta-header {
        display: none;
    }

    nav {
        position: fixed;
        top: 0;
        right: -100%; /* Start off-screen */
        width: 70%;
        height: 100vh;
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(15px);
        transition: right 0.4s ease-in-out;
    }

    nav.active {
        right: 0;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: 40px;
    }

    .menu-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }
    .menu-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    .menu-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 40px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    /* About Section Mobile */
    .about {
        padding: 60px 0;
    }

    .about-content {
        display: flex !important;
        flex-direction: column !important;
        gap: 30px !important;
        text-align: center;
    }

    .about-text {
        margin-bottom: 0;
        order: 1;
    }

    .about-visual {
        order: 2;
        margin-bottom: 20px;
    }

    .about-hero-image {
        max-width: 300px;
    }

    /* Hero Carousel Mobile Styles */
    .hero-carousel-image {
        max-width: 300px;
    }

    .hero-carousel .carousel-nav {
        padding: 0 10px;
    }

    .hero-carousel .carousel-btn {
        width: 35px;
        height: 35px;
        font-size: 20px;
    }

    .carousel-dots {
        bottom: 15px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    /* Mobile styles for feature cards */
    .about-features-grid {
        display: flex !important;
        flex-direction: column !important;
        gap: 15px;
        margin-top: 0;
        order: 3;
        width: 100%;
    }

    .about-feature-card {
        padding: 20px;
        text-align: left;
        width: 100%;
        box-sizing: border-box;
    }

    .about-feature-card h4 {
        font-size: 16px;
    }

    .about-feature-card p {
        font-size: 13px;
    }

    .about-text h2 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .about-text p {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .about-features {
        gap: 20px;
    }

    .about-feature {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .about-feature-icon {
        font-size: 2rem;
    }

    .about-feature h4 {
        font-size: 1.1rem;
    }

    .about-feature p {
        font-size: 0.9rem;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        margin-top: 30px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    /* Fix main stats section mobile */
    .stats {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 0;
        width: 100%;
        align-items: center;
    }

    .stat-item {
        padding: 25px 20px;
        width: 100%;
        max-width: 280px;
        margin: 0;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    /* Contact Section Mobile */
    .contact {
        padding: 60px 0;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-info h2 {
        font-size: 2rem;
        margin-bottom: 20px;
        text-align: center;
    }

    .contact-info p {
        font-size: 1rem;
        margin-bottom: 30px;
        text-align: center;
    }

    .contact-details {
        gap: 20px;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .contact-item-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group textarea {
        font-size: 1rem;
        padding: 12px;
    }

    .submit-btn {
        width: 100%;
        padding: 15px;
        font-size: 1rem;
    }

    /* Footer Mobile Improvements */
    footer {
        padding: 60px 0 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-section {
        padding: 0 20px;
    }

    .footer-section h3 {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }

    .footer-section p,
    .footer-section a {
        font-size: 0.9rem;
    }

    .footer-bottom {
        padding-top: 30px;
        font-size: 0.8rem;
    }

    .footer-bottom p {
        margin-bottom: 5px;
    }
}

/* --- Scroll animations --- */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 1.5s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}
