const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

try {
  // Run the build script
  execSync('npm run build', { stdio: 'inherit' });

  const outputPath = path.join(__dirname, '..', 'dist', 'index.html');

  // Check if the built file exists
  if (!fs.existsSync(outputPath)) {
    console.error('dist/index.html does not exist.');
    process.exit(1);
  }

  const html = fs.readFileSync(outputPath, 'utf8');

  const hasInlineCSS = /<style>[\s\S]*?<\/style>/.test(html);
  const hasInlineJS = /<script>[\s\S]*?<\/script>/.test(html);
  const referencesCSS = html.includes('css/styles.css');
  const referencesJS = html.includes('js/script.js');

  if (!hasInlineCSS || !hasInlineJS || referencesCSS || referencesJS) {
    console.error('Built HTML is missing inlined CSS/JS or still references external files.');
    process.exit(1);
  }

  console.log('Build check passed: dist/index.html has inlined CSS and JS.');
} catch (err) {
  console.error(err.message);
  process.exit(1);
}
