# CloudNextAI Website

A modern, responsive consulting company website for CloudNextAI, specializing in AI, Security, Cloud & Data Engineering solutions.

## 🚀 Features

- **Responsive Design**: Mobile-first approach with comprehensive responsive styles
- **Hero Image Carousel**: Auto-sliding carousel with manual navigation and dot indicators
- **Services Carousel**: Infinite loop carousel showcasing company services
- **Process Carousel**: Interactive carousel displaying the company's proven process
- **Modern UI/UX**: Glassmorphism effects, smooth animations, and gradient designs
- **Contact Form**: Functional contact form with validation
- **Scroll Animations**: Intersection Observer API for scroll-based animations
- **Production Ready**: Optimized build system with CSS/JS minification

## 📁 Project Structure

```
cloudnextai/
├── src/                    # Source files (development)
│   ├── css/
│   │   └── styles.css     # All CSS styles
│   ├── js/
│   │   └── script.js      # All JavaScript functionality
│   ├── images/
│   │   ├── hero1.png      # Hero carousel images
│   │   ├── hero2.png
│   │   └── hero3.png
│   └── index.html         # Clean HTML with external references
├── dist/                  # Production build output
│   ├── images/            # Copied images
│   └── index.html         # Bundled HTML with inline CSS/JS
├── build.js               # Build script
├── package.json           # Project configuration and scripts
└── README.md             # This file
```

## 🛠️ Development

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone or download the project
2. Navigate to the project directory:
   ```bash
   cd CloudNextAI-website
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

### Development Server

To run the development version with live reload:

```bash
npm run dev
```

This will start a local server at `http://localhost:3000` serving the `src/` directory.

### Building for Production

To create a production build:

```bash
npm run build
```

This will:
- Minify CSS and JavaScript
- Bundle all assets into a single HTML file
- Copy images to the dist directory
- Generate build statistics

### Serving Production Build

To test the production build:

```bash
npm run serve:dist
```

This will start a local server at `http://localhost:3001` serving the `dist/` directory.

### Available Scripts

- `npm run build` - Create production build
- `npm run dev` - Start development server
- `npm run serve:src` - Serve source files
- `npm run serve:dist` - Serve production build
- `npm run clean` - Clean dist directory
- `npm start` - Build and serve production
- `npm test` - Build and test production

## 🎨 Key Components

### Hero Carousel
- Auto-sliding image carousel (4-second intervals)
- Manual navigation with previous/next buttons
- Dot indicators for direct slide access
- Hover to pause functionality
- Smooth fade transitions

### Services Carousel
- Infinite loop carousel with cloned slides
- Auto-play with 4-second intervals
- Smooth transitions with proper slide positioning
- Responsive design for mobile devices

### Process Carousel
- Similar to services carousel but with different timing (4.5 seconds)
- Showcases the company's 6-step process
- Interactive indicators and navigation

### Responsive Design
- Mobile-first CSS approach
- Breakpoints optimized for tablets and mobile devices
- Collapsible navigation menu for mobile
- Optimized layouts for different screen sizes

## 🔧 Build System

The build system (`build.js`) provides:

- **CSS Minification**: Removes comments, whitespace, and optimizes selectors
- **JavaScript Minification**: Basic minification removing comments and excess whitespace
- **Asset Bundling**: Combines all CSS and JS into the HTML file
- **Image Copying**: Copies all images to the production directory
- **Build Statistics**: Shows file sizes and compression ratios

### Build Output Example

```
🚀 Starting CloudNextAI production build...

📖 Reading source files...
🗜️  Minifying assets...
🔗 Bundling assets into HTML...
Successfully wrote: dist/index.html
🖼️  Copying images...
Created directory: dist/images
Copied image: hero1.png
Copied image: hero2.png
Copied image: hero3.png

📊 Build Statistics:
Original HTML: 33.68 KB
Original CSS: 28.87 KB
Original JS: 13.28 KB
Total Original: 75.83 KB
Bundled HTML: 62.18 KB
Compression: 18.0% smaller

✅ Build completed successfully!
```

## 🌐 Deployment

The production build creates a single `dist/index.html` file that can be deployed to any web server or hosting platform:

1. Run `npm run build`
2. Upload the contents of the `dist/` directory to your web server
3. Ensure the `images/` directory is accessible
4. The site will be ready to serve

## 📱 Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- CSS Grid and Flexbox support required
- Intersection Observer API support required

## 🎯 Performance

- Optimized CSS with custom properties for theming
- Efficient JavaScript with event delegation
- Lazy loading animations with Intersection Observer
- Minified production assets
- Single HTTP request for main content (excluding images)

## 📄 License

MIT License - feel free to use this project for your own consulting website.

